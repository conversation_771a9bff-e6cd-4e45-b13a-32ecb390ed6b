import { useEffect, useState } from "react";
import "../styles/QR.css"; 
const QRBox = ({
  shortBankName,
  accountNumber,
  ownerName,
  amount,
  description,
}) => {
  const [isLoading, setIsLoading] = useState(true);

  const qrUrl = `https://payment.pay2s.vn/quicklink/${shortBankName}/${accountNumber}/${encodeURIComponent(
    ownerName
  )}?amount=${amount}&memo=${description}&is_mask=0&image=1`;

  useEffect(() => {
    const img = new Image();
    img.src = qrUrl;
    img.onload = () => setIsLoading(false);
  }, [qrUrl]);

  const downloadQR = async () => {
    try {
      const response = await fetch(qrUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = "qr-code.png";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Tải QR thất bại:", error);
    }
  };

  return (
    <div className={`qr-section apply-bg-${shortBankName?.toLowerCase()}`}>
      <div className="qr-box">
        <h5 className="qr-title">Quét mã QR để thanh toán</h5>

        {isLoading ? (
          <div className="qr-loading">Đang tải mã QR...</div>
        ) : (
          <div className="qr-scan-wrapper">
            <img src={qrUrl} alt="QR Code" className="qr-img" />
            <div className="scan-line" />
          </div>
        )}

        <button onClick={downloadQR} className="download-qr-btn">
          Tải về mã QR
        </button>

        <p className="qr-note">
          Sử dụng <strong>ứng dụng ngân hàng</strong> hoặc{" "}
          <strong>ví điện tử</strong> để quét mã
        </p>
      </div>
    </div>
  );
};

export default QRBox;
