import { useEffect } from 'react';

const PaymentWatcher = ({ token }) => {
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const res = await fetch(`http://103.249.116.179:3002/api/generate-redirect?t=${token}`);
        const data = await res.json();

        if (data.success && data.paid) {
          window.location.href = data.redirectUrl;
        }
      } catch (err) {
        console.error("Lỗi khi kiểm tra trạng thái thanh toán:", err);
      }
    }, 3000); // kiểm tra mỗi 3 giây

    return () => clearInterval(interval);
  }, [token]);

  return null;
};

export default PaymentWatcher;
