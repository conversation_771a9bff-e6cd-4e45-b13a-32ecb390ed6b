.qr-scan-wrapper {
  position: relative;
  width: 200px;
  height: 200px;
  margin: auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
}

.scan-line {
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 5%;
  background: linear-gradient(
    to bottom,
    rgba(0, 255, 0, 0) 0%,
    rgb(43 124 81 / 65%) 50%,
    rgba(0, 255, 0, 0) 100%
  );
  animation: scanMove 3s infinite;
  pointer-events: none;
  z-index: 2;
  box-shadow: 0 0 20px 10px rgba(0, 255, 0, 0.2); /* 👈 thêm shadow */
  border-radius: 4px;
}
.qr-img {
  padding: 0px !important;
}
@keyframes scanMove {
  0% {
    top: -10%;
  }
  50% {
    top: 100%;
  }
  100% {
    top: -10%;
  }
}
@media (max-width: 768px) {
  .qr-scan-wrapper {
    width: 150px;
    height: 150px;
  }
}
