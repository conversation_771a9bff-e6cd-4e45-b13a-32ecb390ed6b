import React from "react";
import "../styles/CancelModal.css"; // nếu cần CSS riêng

const CancelModal = ({ isOpen, onClose, onConfirm, companyName }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-box">
        <h4>Hủy giao dịch thanh toán</h4>
        <p>
          Bạn chắc chắn muốn hủy giao dịch thanh toán với{" "}
          <strong>{companyName}</strong>?
        </p>
        <div className="modal-actions">
          <button onClick={onClose} className="modal-cancel-btn">ĐÓNG</button>
          <button onClick={onConfirm} className="modal-confirm-btn">HỦY GIAO DỊCH</button>
        </div>
      </div>
    </div>
  );
};

export default CancelModal;
