import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PaymentPage from "./pages/PaymentPage";

function App() {
  return (
    <Router basename="/v2/gateway">
      {/* ToastContainer nên nằm bên ngoài Routes */}
      <ToastContainer position="top-right" autoClose={2000} />

      <Routes>
        <Route path="/pay" element={<PaymentPage />} />
      </Routes>
    </Router>
  );
}

export default App;
