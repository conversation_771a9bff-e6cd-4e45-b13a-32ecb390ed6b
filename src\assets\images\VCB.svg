<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 30 30" style="enable-background:new 0 0 30 30;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#B02071;}
	.st2{fill:#26A9E1;}
	.st3{fill-rule:evenodd;clip-rule:evenodd;fill:#1E4997;}
	.st4{fill-rule:evenodd;clip-rule:evenodd;fill:#26A9E1;}
	.st5{fill:#1D1D1A;}
	.st6{fill-rule:evenodd;clip-rule:evenodd;fill:#EB2127;}
	.st7{fill:#0D4624;}
	.st8{clip-path:url(#SVGID_2_);fill:#026A42;}
	.st9{clip-path:url(#SVGID_2_);fill:#026B43;}
	.st10{clip-path:url(#SVGID_2_);fill:#026C43;}
	.st11{clip-path:url(#SVGID_2_);fill:#036C43;}
	.st12{clip-path:url(#SVGID_2_);fill:#036D43;}
	.st13{clip-path:url(#SVGID_2_);fill:#036E44;}
	.st14{clip-path:url(#SVGID_2_);fill:#036F44;}
	.st15{clip-path:url(#SVGID_2_);fill:#037044;}
	.st16{clip-path:url(#SVGID_2_);fill:#037045;}
	.st17{clip-path:url(#SVGID_2_);fill:#037145;}
	.st18{clip-path:url(#SVGID_2_);fill:#037245;}
	.st19{clip-path:url(#SVGID_2_);fill:#037345;}
	.st20{clip-path:url(#SVGID_2_);fill:#037346;}
	.st21{clip-path:url(#SVGID_2_);fill:#037446;}
	.st22{clip-path:url(#SVGID_2_);fill:#037546;}
	.st23{clip-path:url(#SVGID_2_);fill:#027647;}
	.st24{clip-path:url(#SVGID_2_);fill:#027747;}
	.st25{clip-path:url(#SVGID_2_);fill:#027847;}
	.st26{clip-path:url(#SVGID_2_);fill:#027948;}
	.st27{clip-path:url(#SVGID_2_);fill:#027A48;}
	.st28{clip-path:url(#SVGID_2_);fill:#027B48;}
	.st29{clip-path:url(#SVGID_2_);fill:#027C49;}
	.st30{clip-path:url(#SVGID_2_);fill:#067D49;}
	.st31{clip-path:url(#SVGID_2_);fill:#0B7D49;}
	.st32{clip-path:url(#SVGID_2_);fill:#0F7E49;}
	.st33{clip-path:url(#SVGID_2_);fill:#127F49;}
	.st34{clip-path:url(#SVGID_2_);fill:#157F49;}
	.st35{clip-path:url(#SVGID_2_);fill:#188049;}
	.st36{clip-path:url(#SVGID_2_);fill:#1A8149;}
	.st37{clip-path:url(#SVGID_2_);fill:#1D8149;}
	.st38{clip-path:url(#SVGID_2_);fill:#1F8249;}
	.st39{clip-path:url(#SVGID_2_);fill:#218249;}
	.st40{clip-path:url(#SVGID_2_);fill:#238349;}
	.st41{clip-path:url(#SVGID_2_);fill:#248449;}
	.st42{clip-path:url(#SVGID_2_);fill:#268449;}
	.st43{clip-path:url(#SVGID_2_);fill:#288549;}
	.st44{clip-path:url(#SVGID_2_);fill:#2A8649;}
	.st45{clip-path:url(#SVGID_2_);fill:#2B8649;}
	.st46{clip-path:url(#SVGID_2_);fill:#2D8749;}
	.st47{clip-path:url(#SVGID_2_);fill:#2E8849;}
	.st48{clip-path:url(#SVGID_2_);fill:#308849;}
	.st49{clip-path:url(#SVGID_2_);fill:#318949;}
	.st50{clip-path:url(#SVGID_2_);fill:#338A49;}
	.st51{clip-path:url(#SVGID_2_);fill:#348A49;}
	.st52{clip-path:url(#SVGID_2_);fill:#358B49;}
	.st53{clip-path:url(#SVGID_2_);fill:#378C49;}
	.st54{clip-path:url(#SVGID_2_);fill:#388D49;}
	.st55{clip-path:url(#SVGID_2_);fill:#3A8D48;}
	.st56{clip-path:url(#SVGID_2_);fill:#3B8E48;}
	.st57{clip-path:url(#SVGID_2_);fill:#3C8F48;}
	.st58{clip-path:url(#SVGID_2_);fill:#3E8F48;}
	.st59{clip-path:url(#SVGID_2_);fill:#3F9048;}
	.st60{clip-path:url(#SVGID_2_);fill:#409148;}
	.st61{clip-path:url(#SVGID_2_);fill:#429148;}
	.st62{clip-path:url(#SVGID_2_);fill:#439248;}
	.st63{clip-path:url(#SVGID_2_);fill:#449348;}
	.st64{clip-path:url(#SVGID_2_);fill:#469448;}
	.st65{clip-path:url(#SVGID_2_);fill:#479448;}
	.st66{clip-path:url(#SVGID_2_);fill:#489548;}
	.st67{clip-path:url(#SVGID_2_);fill:#499648;}
	.st68{clip-path:url(#SVGID_2_);fill:#4B9648;}
	.st69{clip-path:url(#SVGID_2_);fill:#4C9748;}
	.st70{clip-path:url(#SVGID_2_);fill:#4D9848;}
	.st71{clip-path:url(#SVGID_2_);fill:#4E9948;}
	.st72{clip-path:url(#SVGID_2_);fill:#509948;}
	.st73{clip-path:url(#SVGID_2_);fill:#519A48;}
	.st74{clip-path:url(#SVGID_2_);fill:#529B47;}
	.st75{clip-path:url(#SVGID_2_);fill:#539C47;}
	.st76{clip-path:url(#SVGID_2_);fill:#549C47;}
	.st77{clip-path:url(#SVGID_2_);fill:#559D47;}
	.st78{clip-path:url(#SVGID_2_);fill:#569E47;}
	.st79{clip-path:url(#SVGID_2_);fill:#589F47;}
	.st80{clip-path:url(#SVGID_2_);fill:#599F47;}
	.st81{clip-path:url(#SVGID_2_);fill:#5AA047;}
	.st82{clip-path:url(#SVGID_2_);fill:#5BA147;}
	.st83{clip-path:url(#SVGID_2_);fill:#5CA247;}
	.st84{clip-path:url(#SVGID_2_);fill:#5DA346;}
	.st85{clip-path:url(#SVGID_2_);fill:#5EA346;}
	.st86{clip-path:url(#SVGID_2_);fill:#5FA446;}
	.st87{clip-path:url(#SVGID_2_);fill:#61A546;}
	.st88{clip-path:url(#SVGID_2_);fill:#62A646;}
	.st89{clip-path:url(#SVGID_2_);fill:#63A746;}
	.st90{clip-path:url(#SVGID_2_);fill:#64A746;}
	.st91{clip-path:url(#SVGID_2_);fill:#65A846;}
	.st92{clip-path:url(#SVGID_2_);fill:#66A946;}
	.st93{clip-path:url(#SVGID_2_);fill:#67AA45;}
	.st94{clip-path:url(#SVGID_2_);fill:#69AB45;}
	.st95{clip-path:url(#SVGID_2_);fill:#6AAB45;}
	.st96{clip-path:url(#SVGID_2_);fill:#6BAC45;}
	.st97{clip-path:url(#SVGID_2_);fill:#6CAD45;}
	.st98{clip-path:url(#SVGID_2_);fill:#6DAE45;}
	.st99{clip-path:url(#SVGID_2_);fill:#6EAF45;}
	.st100{clip-path:url(#SVGID_2_);fill:#6FB044;}
	.st101{clip-path:url(#SVGID_2_);fill:#70B144;}
	.st102{clip-path:url(#SVGID_2_);fill:#72B144;}
	.st103{clip-path:url(#SVGID_2_);fill:#73B244;}
	.st104{clip-path:url(#SVGID_2_);fill:#74B344;}
	.st105{clip-path:url(#SVGID_2_);fill:#75B444;}
	.st106{clip-path:url(#SVGID_2_);fill:#76B544;}
	.st107{clip-path:url(#SVGID_2_);fill:#77B644;}
	.st108{clip-path:url(#SVGID_2_);fill:#79B743;}
	.st109{clip-path:url(#SVGID_2_);fill:#7AB843;}
	.st110{clip-path:url(#SVGID_2_);fill:#7BB943;}
	.st111{clip-path:url(#SVGID_2_);fill:#7CBA43;}
	.st112{clip-path:url(#SVGID_2_);fill:#7DBB43;}
	.st113{clip-path:url(#SVGID_2_);fill:#7EBC43;}
	.st114{clip-path:url(#SVGID_2_);fill:#80BD43;}
	.st115{clip-path:url(#SVGID_2_);fill:#81BF42;}
	.st116{clip-path:url(#SVGID_2_);fill:#82C042;}
	.st117{clip-path:url(#SVGID_2_);fill:#83C142;}
	.st118{clip-path:url(#SVGID_2_);fill:#84C342;}
	.st119{clip-path:url(#SVGID_2_);fill:#86C442;}
	.st120{clip-path:url(#SVGID_4_);fill:#026A42;}
	.st121{clip-path:url(#SVGID_4_);fill:#026B43;}
	.st122{clip-path:url(#SVGID_4_);fill:#026C43;}
	.st123{clip-path:url(#SVGID_4_);fill:#036C43;}
	.st124{clip-path:url(#SVGID_4_);fill:#036D43;}
	.st125{clip-path:url(#SVGID_4_);fill:#036E44;}
	.st126{clip-path:url(#SVGID_4_);fill:#036F44;}
	.st127{clip-path:url(#SVGID_4_);fill:#037044;}
	.st128{clip-path:url(#SVGID_4_);fill:#037045;}
	.st129{clip-path:url(#SVGID_4_);fill:#037145;}
	.st130{clip-path:url(#SVGID_4_);fill:#037245;}
	.st131{clip-path:url(#SVGID_4_);fill:#037345;}
	.st132{clip-path:url(#SVGID_4_);fill:#037346;}
	.st133{clip-path:url(#SVGID_4_);fill:#037446;}
	.st134{clip-path:url(#SVGID_4_);fill:#037546;}
	.st135{clip-path:url(#SVGID_4_);fill:#027647;}
	.st136{clip-path:url(#SVGID_4_);fill:#027747;}
	.st137{clip-path:url(#SVGID_4_);fill:#027847;}
	.st138{clip-path:url(#SVGID_4_);fill:#027948;}
	.st139{clip-path:url(#SVGID_4_);fill:#027A48;}
	.st140{clip-path:url(#SVGID_4_);fill:#027B48;}
	.st141{clip-path:url(#SVGID_4_);fill:#027C49;}
	.st142{clip-path:url(#SVGID_4_);fill:#067D49;}
	.st143{clip-path:url(#SVGID_4_);fill:#0B7D49;}
	.st144{clip-path:url(#SVGID_4_);fill:#0F7E49;}
	.st145{clip-path:url(#SVGID_4_);fill:#127F49;}
	.st146{clip-path:url(#SVGID_4_);fill:#157F49;}
	.st147{clip-path:url(#SVGID_4_);fill:#188049;}
	.st148{clip-path:url(#SVGID_4_);fill:#1A8149;}
	.st149{clip-path:url(#SVGID_4_);fill:#1D8149;}
	.st150{clip-path:url(#SVGID_4_);fill:#1F8249;}
	.st151{clip-path:url(#SVGID_4_);fill:#218249;}
	.st152{clip-path:url(#SVGID_4_);fill:#238349;}
	.st153{clip-path:url(#SVGID_4_);fill:#248449;}
	.st154{clip-path:url(#SVGID_4_);fill:#268449;}
	.st155{clip-path:url(#SVGID_4_);fill:#288549;}
	.st156{clip-path:url(#SVGID_4_);fill:#2A8649;}
	.st157{clip-path:url(#SVGID_4_);fill:#2B8649;}
	.st158{clip-path:url(#SVGID_4_);fill:#2D8749;}
	.st159{clip-path:url(#SVGID_4_);fill:#2E8849;}
	.st160{clip-path:url(#SVGID_4_);fill:#308849;}
	.st161{clip-path:url(#SVGID_4_);fill:#318949;}
	.st162{clip-path:url(#SVGID_4_);fill:#338A49;}
	.st163{clip-path:url(#SVGID_4_);fill:#348A49;}
	.st164{clip-path:url(#SVGID_4_);fill:#358B49;}
	.st165{clip-path:url(#SVGID_4_);fill:#378C49;}
	.st166{clip-path:url(#SVGID_4_);fill:#388D49;}
	.st167{clip-path:url(#SVGID_4_);fill:#3A8D48;}
	.st168{clip-path:url(#SVGID_4_);fill:#3B8E48;}
	.st169{clip-path:url(#SVGID_4_);fill:#3C8F48;}
	.st170{clip-path:url(#SVGID_4_);fill:#3E8F48;}
	.st171{clip-path:url(#SVGID_4_);fill:#3F9048;}
	.st172{clip-path:url(#SVGID_4_);fill:#409148;}
	.st173{clip-path:url(#SVGID_4_);fill:#429148;}
	.st174{clip-path:url(#SVGID_4_);fill:#439248;}
	.st175{clip-path:url(#SVGID_4_);fill:#449348;}
	.st176{clip-path:url(#SVGID_4_);fill:#469448;}
	.st177{clip-path:url(#SVGID_4_);fill:#479448;}
	.st178{clip-path:url(#SVGID_4_);fill:#489548;}
	.st179{clip-path:url(#SVGID_4_);fill:#499648;}
	.st180{clip-path:url(#SVGID_4_);fill:#4B9648;}
	.st181{clip-path:url(#SVGID_4_);fill:#4C9748;}
	.st182{clip-path:url(#SVGID_4_);fill:#4D9848;}
	.st183{clip-path:url(#SVGID_4_);fill:#4E9948;}
	.st184{clip-path:url(#SVGID_4_);fill:#509948;}
	.st185{clip-path:url(#SVGID_4_);fill:#519A48;}
	.st186{clip-path:url(#SVGID_4_);fill:#529B47;}
	.st187{clip-path:url(#SVGID_4_);fill:#539C47;}
	.st188{clip-path:url(#SVGID_4_);fill:#549C47;}
	.st189{clip-path:url(#SVGID_4_);fill:#559D47;}
	.st190{clip-path:url(#SVGID_4_);fill:#569E47;}
	.st191{clip-path:url(#SVGID_4_);fill:#589F47;}
	.st192{clip-path:url(#SVGID_4_);fill:#599F47;}
	.st193{clip-path:url(#SVGID_4_);fill:#5AA047;}
	.st194{clip-path:url(#SVGID_4_);fill:#5BA147;}
	.st195{clip-path:url(#SVGID_4_);fill:#5CA247;}
	.st196{clip-path:url(#SVGID_4_);fill:#5DA346;}
	.st197{clip-path:url(#SVGID_4_);fill:#5EA346;}
	.st198{clip-path:url(#SVGID_4_);fill:#5FA446;}
	.st199{clip-path:url(#SVGID_4_);fill:#61A546;}
	.st200{clip-path:url(#SVGID_4_);fill:#62A646;}
	.st201{clip-path:url(#SVGID_4_);fill:#63A746;}
	.st202{clip-path:url(#SVGID_4_);fill:#64A746;}
	.st203{clip-path:url(#SVGID_4_);fill:#65A846;}
	.st204{clip-path:url(#SVGID_4_);fill:#66A946;}
	.st205{clip-path:url(#SVGID_4_);fill:#67AA45;}
	.st206{clip-path:url(#SVGID_4_);fill:#69AB45;}
	.st207{clip-path:url(#SVGID_4_);fill:#6AAB45;}
	.st208{clip-path:url(#SVGID_4_);fill:#6BAC45;}
	.st209{clip-path:url(#SVGID_4_);fill:#6CAD45;}
	.st210{clip-path:url(#SVGID_4_);fill:#6DAE45;}
	.st211{clip-path:url(#SVGID_4_);fill:#6EAF45;}
	.st212{clip-path:url(#SVGID_4_);fill:#6FB044;}
	.st213{clip-path:url(#SVGID_4_);fill:#70B144;}
	.st214{clip-path:url(#SVGID_4_);fill:#72B144;}
	.st215{clip-path:url(#SVGID_4_);fill:#73B244;}
	.st216{clip-path:url(#SVGID_4_);fill:#74B344;}
	.st217{clip-path:url(#SVGID_4_);fill:#75B444;}
	.st218{clip-path:url(#SVGID_4_);fill:#76B544;}
	.st219{clip-path:url(#SVGID_4_);fill:#77B644;}
	.st220{clip-path:url(#SVGID_4_);fill:#79B743;}
	.st221{clip-path:url(#SVGID_4_);fill:#7AB843;}
	.st222{clip-path:url(#SVGID_4_);fill:#7BB943;}
	.st223{clip-path:url(#SVGID_4_);fill:#7CBA43;}
	.st224{clip-path:url(#SVGID_4_);fill:#7DBB43;}
	.st225{clip-path:url(#SVGID_4_);fill:#7EBC43;}
	.st226{clip-path:url(#SVGID_4_);fill:#80BD43;}
	.st227{clip-path:url(#SVGID_4_);fill:#81BF42;}
	.st228{clip-path:url(#SVGID_4_);fill:#82C042;}
	.st229{clip-path:url(#SVGID_4_);fill:#83C142;}
	.st230{clip-path:url(#SVGID_4_);fill:#84C342;}
	.st231{clip-path:url(#SVGID_4_);fill:#86C442;}
	.st232{fill:#068547;}
	.st233{fill-rule:evenodd;clip-rule:evenodd;fill:#E9262A;}
	.st234{fill:#394A9F;}
	.st235{fill:#EA304C;}
</style>
<g>
	<g>
		<path class="st0" d="M25.91,29.88H4.09c-2.18,0-3.97-1.78-3.97-3.97V4.09c0-2.18,1.78-3.97,3.97-3.97h21.82
			c2.18,0,3.97,1.78,3.97,3.97v21.82C29.88,28.09,28.09,29.88,25.91,29.88z"/>
		<path class="st7" d="M1.2,28.8C0.46,28.06,0,27.04,0,25.91L0,4.09C0,3,0.43,1.98,1.2,1.2C1.98,0.43,3,0,4.09,0l21.82,0
			C27,0,28.02,0.43,28.8,1.2C29.57,1.98,30,3,30,4.09v21.82c0,1.09-0.43,2.11-1.2,2.89C28.02,29.57,27,30,25.91,30H4.09
			C2.96,30,1.94,29.54,1.2,28.8z M28.62,1.38c-0.73-0.73-1.69-1.13-2.71-1.13l-21.82,0c-1.02,0-1.99,0.4-2.71,1.13
			C0.65,2.1,0.25,3.07,0.25,4.09l0,21.82c0,2.12,1.72,3.84,3.84,3.84h21.82c1.02,0,1.99-0.4,2.71-1.13
			c0.73-0.73,1.13-1.69,1.13-2.71V4.09C29.75,3.07,29.35,2.1,28.62,1.38z"/>
	</g>
	<g>
		<g>
			<g>
				<defs>
					<path id="SVGID_1_" d="M11.88,4.42c-1.57,0.32-2.14,1.03-2.09,2.05c0.08,1.44,1.66,4.18,2.42,5.4
						c1.32,2.14,2.88,4.06,4.61,5.84c1.41,1.39,2.29,1.69,3.79-0.36c1.28-1.69,2.47-3.37,3.48-5.47c0.45-0.92,0.86-1.93,1.23-3.05
						c0.4-1.32,0.25-2.48-1.03-3.33c1.24,0.61,1.78,1.72,1.86,2.95c0.08,1.19-0.28,2.48-0.55,3.43c-0.68,2.37-1.54,4.77-2.64,6.92
						c-0.82,1.62-1.88,3.58-2.94,5.03c-2.33,3.21-4.56,2.17-7.18,0c-1.66-1.37-3.25-3.11-4.63-5.02c-1.39-1.92-2.91-4.76-3.81-6.94
						C3.17,8.87,4.13,7.34,5.66,6.37C7.33,5.31,9.84,4.7,11.88,4.42z"/>
				</defs>
				<clipPath id="SVGID_2_">
					<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
				</clipPath>
				<polygon class="st8" points="16.59,27.85 29.23,9.8 32.07,12.64 16.73,27.99 				"/>
				<polygon class="st8" points="16.36,27.62 29,9.57 29.23,9.8 16.59,27.85 				"/>
				<polygon class="st9" points="16.13,27.39 28.77,9.34 29,9.57 16.36,27.62 				"/>
				<polygon class="st10" points="15.9,27.16 28.54,9.1 28.77,9.34 16.13,27.39 				"/>
				<polygon class="st11" points="15.66,26.93 28.3,8.87 28.54,9.1 15.9,27.16 				"/>
				<polygon class="st12" points="15.43,26.69 28.07,8.64 28.3,8.87 15.66,26.93 				"/>
				<polygon class="st13" points="15.2,26.46 27.84,8.41 28.07,8.64 15.43,26.69 				"/>
				<polygon class="st13" points="14.97,26.23 27.61,8.18 27.84,8.41 15.2,26.46 				"/>
				<polygon class="st14" points="14.74,26 27.38,7.95 27.61,8.18 14.97,26.23 				"/>
				<polygon class="st15" points="14.51,25.77 27.15,7.72 27.38,7.95 14.74,26 				"/>
				<polygon class="st16" points="14.28,25.54 26.92,7.49 27.15,7.72 14.51,25.77 				"/>
				<polygon class="st17" points="14.05,25.31 26.69,7.26 26.92,7.49 14.28,25.54 				"/>
				<polygon class="st18" points="13.82,25.08 26.46,7.02 26.69,7.26 14.05,25.31 				"/>
				<polygon class="st19" points="13.58,24.85 26.22,6.79 26.46,7.02 13.82,25.08 				"/>
				<polygon class="st20" points="13.35,24.61 25.99,6.56 26.22,6.79 13.58,24.85 				"/>
				<polygon class="st21" points="13.12,24.38 25.76,6.33 25.99,6.56 13.35,24.61 				"/>
				<polygon class="st22" points="12.89,24.15 25.53,6.1 25.76,6.33 13.12,24.38 				"/>
				<polygon class="st22" points="12.66,23.92 25.3,5.87 25.53,6.1 12.89,24.15 				"/>
				<polygon class="st23" points="12.43,23.69 25.07,5.64 25.3,5.87 12.66,23.92 				"/>
				<polygon class="st24" points="12.2,23.46 24.84,5.41 25.07,5.64 12.43,23.69 				"/>
				<polygon class="st24" points="11.97,23.23 24.61,5.18 24.84,5.41 12.2,23.46 				"/>
				<polygon class="st25" points="11.74,23 24.38,4.94 24.61,5.18 11.97,23.23 				"/>
				<polygon class="st26" points="11.5,22.77 24.14,4.71 24.38,4.94 11.74,23 				"/>
				<polygon class="st26" points="11.27,22.53 23.91,4.48 24.14,4.71 11.5,22.77 				"/>
				<polygon class="st27" points="11.04,22.3 23.68,4.25 23.91,4.48 11.27,22.53 				"/>
				<polygon class="st28" points="10.81,22.07 23.45,4.02 23.68,4.25 11.04,22.3 				"/>
				<polygon class="st28" points="10.58,21.84 23.22,3.79 23.45,4.02 10.81,22.07 				"/>
				<polygon class="st29" points="10.35,21.61 22.99,3.56 23.22,3.79 10.58,21.84 				"/>
				<polygon class="st29" points="10.25,21.51 22.89,3.46 22.99,3.56 10.35,21.61 				"/>
				<polygon class="st30" points="10.14,21.4 22.78,3.35 22.89,3.46 10.25,21.51 				"/>
				<polygon class="st31" points="10.04,21.3 22.68,3.25 22.78,3.35 10.14,21.4 				"/>
				<polygon class="st32" points="9.94,21.2 22.58,3.15 22.68,3.25 10.04,21.3 				"/>
				<polygon class="st33" points="9.83,21.1 22.47,3.04 22.58,3.15 9.94,21.2 				"/>
				<polygon class="st34" points="9.73,20.99 22.37,2.94 22.47,3.04 9.83,21.1 				"/>
				<polygon class="st35" points="9.63,20.89 22.27,2.84 22.37,2.94 9.73,20.99 				"/>
				<polygon class="st36" points="9.53,20.79 22.17,2.74 22.27,2.84 9.63,20.89 				"/>
				<polygon class="st37" points="9.42,20.68 22.06,2.63 22.17,2.74 9.53,20.79 				"/>
				<polygon class="st38" points="9.32,20.58 21.96,2.53 22.06,2.63 9.42,20.68 				"/>
				<polygon class="st39" points="9.22,20.48 21.86,2.43 21.96,2.53 9.32,20.58 				"/>
				<polygon class="st40" points="9.11,20.38 21.75,2.32 21.86,2.43 9.22,20.48 				"/>
				<polygon class="st41" points="9.01,20.27 21.65,2.22 21.75,2.32 9.11,20.38 				"/>
				<polygon class="st42" points="8.91,20.17 21.55,2.12 21.65,2.22 9.01,20.27 				"/>
				<polygon class="st43" points="8.81,20.07 21.44,2.02 21.49,2.06 21.55,2.12 8.91,20.17 				"/>
				<polygon class="st44" points="8.7,19.96 21.32,1.94 21.44,2.02 8.81,20.07 				"/>
				<polygon class="st45" points="8.6,19.86 21.21,1.86 21.32,1.94 8.7,19.96 				"/>
				<polygon class="st46" points="8.5,19.76 21.09,1.78 21.21,1.86 8.6,19.86 				"/>
				<polygon class="st47" points="8.39,19.66 20.97,1.7 21.09,1.78 8.5,19.76 				"/>
				<polygon class="st48" points="8.29,19.55 20.85,1.61 20.97,1.7 8.39,19.66 				"/>
				<polygon class="st49" points="8.19,19.45 20.74,1.53 20.85,1.61 8.29,19.55 				"/>
				<polygon class="st50" points="8.09,19.35 20.62,1.45 20.74,1.53 8.19,19.45 				"/>
				<polygon class="st51" points="7.98,19.24 20.5,1.37 20.62,1.45 8.09,19.35 				"/>
				<polygon class="st52" points="7.88,19.14 20.38,1.28 20.5,1.37 7.98,19.24 				"/>
				<polygon class="st53" points="7.78,19.04 20.27,1.2 20.38,1.28 7.88,19.14 				"/>
				<polygon class="st54" points="7.67,18.94 20.15,1.12 20.27,1.2 7.78,19.04 				"/>
				<polygon class="st55" points="7.57,18.83 20.03,1.04 20.15,1.12 7.67,18.94 				"/>
				<polygon class="st56" points="7.47,18.73 19.91,0.96 20.03,1.04 7.57,18.83 				"/>
				<polygon class="st57" points="7.37,18.63 19.8,0.87 19.91,0.96 7.47,18.73 				"/>
				<polygon class="st58" points="7.26,18.52 19.68,0.79 19.8,0.87 7.37,18.63 				"/>
				<polygon class="st59" points="7.16,18.42 19.56,0.71 19.68,0.79 7.26,18.52 				"/>
				<polygon class="st60" points="7.06,18.32 19.44,0.63 19.56,0.71 7.16,18.42 				"/>
				<polygon class="st61" points="6.95,18.22 19.33,0.55 19.44,0.63 7.06,18.32 				"/>
				<polygon class="st62" points="6.85,18.11 19.21,0.46 19.33,0.55 6.95,18.22 				"/>
				<polygon class="st63" points="6.75,18.01 19.09,0.38 19.21,0.46 6.85,18.11 				"/>
				<polygon class="st64" points="6.65,17.91 18.98,0.3 19.09,0.38 6.75,18.01 				"/>
				<polygon class="st65" points="6.54,17.8 18.86,0.22 18.98,0.3 6.65,17.91 				"/>
				<polygon class="st66" points="6.44,17.7 18.74,0.13 18.86,0.22 6.54,17.8 				"/>
				<polygon class="st67" points="6.34,17.6 18.62,0.05 18.74,0.13 6.44,17.7 				"/>
				<polygon class="st68" points="6.23,17.5 18.51,-0.03 18.62,0.05 6.34,17.6 				"/>
				<polygon class="st69" points="6.13,17.39 18.39,-0.11 18.51,-0.03 6.23,17.5 				"/>
				<polygon class="st70" points="6.03,17.29 18.27,-0.19 18.39,-0.11 6.13,17.39 				"/>
				<polygon class="st71" points="5.93,17.19 18.15,-0.28 18.27,-0.19 6.03,17.29 				"/>
				<polygon class="st72" points="5.82,17.08 18.04,-0.36 18.15,-0.28 5.93,17.19 				"/>
				<polygon class="st73" points="5.72,16.98 17.92,-0.44 18.04,-0.36 5.82,17.08 				"/>
				<polygon class="st74" points="5.62,16.88 17.8,-0.52 17.92,-0.44 5.72,16.98 				"/>
				<polygon class="st75" points="5.51,16.78 17.68,-0.61 17.8,-0.52 5.62,16.88 				"/>
				<polygon class="st76" points="5.41,16.67 17.57,-0.69 17.68,-0.61 5.51,16.78 				"/>
				<polygon class="st77" points="5.31,16.57 17.45,-0.77 17.57,-0.69 5.41,16.67 				"/>
				<polygon class="st78" points="5.21,16.47 17.33,-0.85 17.45,-0.77 5.31,16.57 				"/>
				<polygon class="st79" points="5.1,16.36 17.22,-0.93 17.33,-0.85 5.21,16.47 				"/>
				<polygon class="st80" points="5,16.26 17.1,-1.02 17.22,-0.93 5.1,16.36 				"/>
				<polygon class="st81" points="4.9,16.16 16.98,-1.1 17.1,-1.02 5,16.26 				"/>
				<polygon class="st82" points="4.79,16.06 16.86,-1.18 16.98,-1.1 4.9,16.16 				"/>
				<polygon class="st83" points="4.69,15.95 16.75,-1.26 16.86,-1.18 4.79,16.06 				"/>
				<polygon class="st84" points="4.59,15.85 16.63,-1.34 16.75,-1.26 4.69,15.95 				"/>
				<polygon class="st85" points="4.49,15.75 16.51,-1.43 16.63,-1.34 4.59,15.85 				"/>
				<polygon class="st86" points="4.38,15.64 16.39,-1.51 16.51,-1.43 4.49,15.75 				"/>
				<polygon class="st87" points="4.28,15.54 16.28,-1.59 16.39,-1.51 4.38,15.64 				"/>
				<polygon class="st88" points="4.18,15.44 16.16,-1.67 16.28,-1.59 4.28,15.54 				"/>
				<polygon class="st89" points="4.07,15.34 16.04,-1.76 16.16,-1.67 4.18,15.44 				"/>
				<polygon class="st90" points="3.97,15.23 15.92,-1.84 16.04,-1.76 4.07,15.34 				"/>
				<polygon class="st91" points="3.87,15.13 15.81,-1.92 15.92,-1.84 3.97,15.23 				"/>
				<polygon class="st92" points="3.77,15.03 15.69,-2 15.81,-1.92 3.87,15.13 				"/>
				<polygon class="st93" points="3.66,14.92 15.57,-2.08 15.69,-2 3.77,15.03 				"/>
				<polygon class="st94" points="3.56,14.82 15.46,-2.17 15.57,-2.08 3.66,14.92 				"/>
				<polygon class="st95" points="3.46,14.72 15.34,-2.25 15.46,-2.17 3.56,14.82 				"/>
				<polygon class="st96" points="3.35,14.62 15.22,-2.33 15.34,-2.25 3.46,14.72 				"/>
				<polygon class="st97" points="3.25,14.51 15.1,-2.41 15.22,-2.33 3.35,14.62 				"/>
				<polygon class="st98" points="3.15,14.41 14.99,-2.49 15.1,-2.41 3.25,14.51 				"/>
				<polygon class="st99" points="3.05,14.31 14.87,-2.58 14.99,-2.49 3.15,14.41 				"/>
				<polygon class="st100" points="2.94,14.2 14.75,-2.66 14.87,-2.58 3.05,14.31 				"/>
				<polygon class="st101" points="2.84,14.1 14.63,-2.74 14.75,-2.66 2.94,14.2 				"/>
				<polygon class="st102" points="2.74,14 14.52,-2.82 14.63,-2.74 2.84,14.1 				"/>
				<polygon class="st103" points="2.63,13.9 14.4,-2.91 14.52,-2.82 2.74,14 				"/>
				<polygon class="st104" points="2.53,13.79 13.86,-2.39 14.39,-2.91 14.4,-2.91 2.63,13.9 				"/>
				<polygon class="st105" points="2.43,13.69 13.28,-1.8 13.86,-2.39 2.53,13.79 				"/>
				<polygon class="st106" points="2.33,13.59 12.7,-1.22 13.28,-1.8 2.43,13.69 				"/>
				<polygon class="st107" points="2.22,13.48 12.11,-0.64 12.7,-1.22 2.33,13.59 				"/>
				<polygon class="st108" points="2.12,13.38 11.53,-0.05 12.11,-0.64 2.22,13.48 				"/>
				<polygon class="st109" points="2.02,13.28 10.95,0.53 11.53,-0.05 2.12,13.38 				"/>
				<polygon class="st110" points="1.91,13.18 10.36,1.11 10.95,0.53 2.02,13.28 				"/>
				<polygon class="st111" points="1.81,13.07 9.78,1.7 10.36,1.11 1.91,13.18 				"/>
				<polygon class="st112" points="1.71,12.97 9.2,2.28 9.78,1.7 1.81,13.07 				"/>
				<polygon class="st113" points="1.61,12.87 8.61,2.86 9.2,2.28 1.71,12.97 				"/>
				<polygon class="st114" points="1.5,12.76 8.03,3.45 8.61,2.86 1.61,12.87 				"/>
				<polygon class="st115" points="1.4,12.66 7.45,4.03 8.03,3.45 1.5,12.76 				"/>
				<polygon class="st116" points="1.3,12.56 6.86,4.61 7.45,4.03 1.4,12.66 				"/>
				<polygon class="st117" points="1.19,12.46 6.28,5.2 6.86,4.61 1.3,12.56 				"/>
				<polygon class="st118" points="1.09,12.35 5.7,5.78 6.28,5.2 1.19,12.46 				"/>
				<polygon class="st119" points="0.99,12.25 5.11,6.36 5.7,5.78 1.09,12.35 				"/>
				<polygon class="st119" points="5.11,6.36 0.99,12.25 0.11,11.37 				"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_3_" d="M11.82,11.24c2.43-0.36,8.73-0.75,10.94,0.39c0.74,0.38,0.76,0.9,0.69,1.49
						c0.22-0.4,0.44-0.81,0.65-1.24c0.45-0.92,0.86-1.93,1.23-3.05c0.4-1.32,0.25-2.48-1.03-3.33l0,0
						c-0.48-0.25-1.03-0.43-1.53-0.58c-1.46-0.42-3.07-0.6-4.7-0.71c-0.67-0.05-1.37-0.05-2.08-0.04c-1.28,0.01-2.69,0.08-4.1,0.27
						C10.31,4.74,9.74,5.45,9.8,6.47C9.86,7.7,11.01,9.87,11.82,11.24z"/>
				</defs>
				<clipPath id="SVGID_4_">
					<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
				</clipPath>
				<polygon class="st120" points="15.84,-0.78 9.23,8.65 7.82,7.24 				"/>
				<polygon class="st120" points="16.37,-1.32 16.38,-1.31 9.33,8.76 9.23,8.65 15.84,-0.78 				"/>
				<polygon class="st121" points="16.38,-1.31 16.5,-1.23 9.44,8.86 9.33,8.76 				"/>
				<polygon class="st122" points="16.5,-1.23 16.62,-1.14 9.54,8.96 9.44,8.86 				"/>
				<polygon class="st123" points="16.62,-1.14 16.73,-1.06 9.64,9.06 9.54,8.96 				"/>
				<polygon class="st124" points="16.73,-1.06 16.85,-0.98 9.74,9.17 9.64,9.06 				"/>
				<polygon class="st125" points="16.85,-0.98 16.97,-0.9 9.85,9.27 9.74,9.17 				"/>
				<polygon class="st125" points="16.97,-0.9 17.08,-0.82 9.95,9.37 9.85,9.27 				"/>
				<polygon class="st126" points="17.08,-0.82 17.2,-0.73 10.05,9.47 9.95,9.37 				"/>
				<polygon class="st127" points="17.2,-0.73 17.32,-0.65 10.16,9.58 10.05,9.47 				"/>
				<polygon class="st128" points="17.32,-0.65 17.44,-0.57 10.26,9.68 10.16,9.58 				"/>
				<polygon class="st129" points="17.44,-0.57 17.55,-0.49 10.36,9.78 10.26,9.68 				"/>
				<polygon class="st130" points="17.55,-0.49 17.67,-0.41 10.46,9.88 10.36,9.78 				"/>
				<polygon class="st131" points="17.67,-0.41 17.79,-0.33 10.57,9.99 10.46,9.88 				"/>
				<polygon class="st132" points="17.79,-0.33 17.9,-0.24 10.67,10.09 10.57,9.99 				"/>
				<polygon class="st133" points="17.9,-0.24 18.02,-0.16 10.77,10.19 10.67,10.09 				"/>
				<polygon class="st134" points="18.02,-0.16 18.14,-0.08 10.87,10.29 10.77,10.19 				"/>
				<polygon class="st134" points="18.14,-0.08 18.25,0 10.98,10.4 10.87,10.29 				"/>
				<polygon class="st135" points="18.25,0 18.37,0.08 11.08,10.5 10.98,10.4 				"/>
				<polygon class="st136" points="18.37,0.08 18.49,0.17 11.18,10.6 11.08,10.5 				"/>
				<polygon class="st136" points="18.49,0.17 18.61,0.25 11.28,10.7 11.18,10.6 				"/>
				<polygon class="st137" points="18.61,0.25 18.72,0.33 11.39,10.81 11.28,10.7 				"/>
				<polygon class="st138" points="18.72,0.33 18.84,0.41 11.49,10.91 11.39,10.81 				"/>
				<polygon class="st138" points="18.84,0.41 18.96,0.49 11.59,11.01 11.49,10.91 				"/>
				<polygon class="st139" points="18.96,0.49 19.07,0.58 11.69,11.12 11.59,11.01 				"/>
				<polygon class="st140" points="19.07,0.58 19.19,0.66 11.8,11.22 11.69,11.12 				"/>
				<polygon class="st140" points="19.19,0.66 19.31,0.74 11.9,11.32 11.8,11.22 				"/>
				<polygon class="st141" points="19.31,0.74 19.42,0.82 12,11.42 11.9,11.32 				"/>
				<polygon class="st141" points="19.42,0.82 19.51,0.88 12.07,11.49 12,11.42 				"/>
				<polygon class="st142" points="19.51,0.88 19.59,0.94 12.14,11.56 12.07,11.49 				"/>
				<polygon class="st143" points="19.59,0.94 19.67,0.99 12.21,11.64 12.14,11.56 				"/>
				<polygon class="st144" points="19.67,0.99 19.75,1.05 12.29,11.71 12.21,11.64 				"/>
				<polygon class="st145" points="19.75,1.05 19.83,1.11 12.36,11.78 12.29,11.71 				"/>
				<polygon class="st146" points="19.83,1.11 19.91,1.16 12.43,11.85 12.36,11.78 				"/>
				<polygon class="st147" points="19.91,1.16 19.99,1.22 12.5,11.92 12.43,11.85 				"/>
				<polygon class="st148" points="19.99,1.22 20.07,1.28 12.57,11.99 12.5,11.92 				"/>
				<polygon class="st149" points="20.07,1.28 20.15,1.33 12.64,12.06 12.57,11.99 				"/>
				<polygon class="st150" points="20.15,1.33 20.23,1.39 12.71,12.13 12.64,12.06 				"/>
				<polygon class="st151" points="20.23,1.39 20.32,1.45 12.78,12.2 12.71,12.13 				"/>
				<polygon class="st152" points="20.32,1.45 20.4,1.5 12.85,12.28 12.78,12.2 				"/>
				<polygon class="st153" points="20.4,1.5 20.48,1.56 12.92,12.35 12.85,12.28 				"/>
				<polygon class="st154" points="20.48,1.56 20.56,1.62 13,12.42 12.92,12.35 				"/>
				<polygon class="st155" points="20.56,1.62 20.64,1.67 13.07,12.49 13,12.42 				"/>
				<polygon class="st156" points="20.64,1.67 20.72,1.73 13.14,12.56 13.07,12.49 				"/>
				<polygon class="st157" points="20.72,1.73 20.8,1.79 13.21,12.63 13.14,12.56 				"/>
				<polygon class="st158" points="20.8,1.79 20.88,1.84 13.28,12.7 13.21,12.63 				"/>
				<polygon class="st159" points="20.88,1.84 20.96,1.9 13.35,12.77 13.28,12.7 				"/>
				<polygon class="st160" points="20.96,1.9 21.04,1.96 13.42,12.84 13.35,12.77 				"/>
				<polygon class="st161" points="21.04,1.96 21.13,2.01 13.49,12.91 13.42,12.84 				"/>
				<polygon class="st162" points="21.13,2.01 21.21,2.07 13.56,12.99 13.49,12.91 				"/>
				<polygon class="st163" points="21.21,2.07 21.29,2.13 13.63,13.06 13.56,12.99 				"/>
				<polygon class="st164" points="21.29,2.13 21.37,2.18 13.71,13.13 13.63,13.06 				"/>
				<polygon class="st165" points="21.37,2.18 21.45,2.24 13.78,13.2 13.71,13.13 				"/>
				<polygon class="st166" points="21.45,2.24 21.53,2.3 13.85,13.27 13.78,13.2 				"/>
				<polygon class="st167" points="21.53,2.3 21.53,2.3 21.6,2.37 13.92,13.34 13.85,13.27 				"/>
				<polygon class="st168" points="21.67,2.44 13.99,13.41 13.92,13.34 21.6,2.37 				"/>
				<polygon class="st169" points="21.74,2.51 14.06,13.48 13.99,13.41 21.67,2.44 				"/>
				<polygon class="st170" points="21.82,2.58 14.13,13.55 14.06,13.48 21.74,2.51 				"/>
				<polygon class="st171" points="21.89,2.65 14.2,13.62 14.13,13.55 21.82,2.58 				"/>
				<polygon class="st172" points="21.96,2.72 14.27,13.7 14.2,13.62 21.89,2.65 				"/>
				<polygon class="st173" points="22.03,2.79 14.34,13.77 14.27,13.7 21.96,2.72 				"/>
				<polygon class="st174" points="22.1,2.86 14.42,13.84 14.34,13.77 22.03,2.79 				"/>
				<polygon class="st175" points="22.17,2.94 14.49,13.91 14.42,13.84 22.1,2.86 				"/>
				<polygon class="st176" points="22.24,3.01 14.56,13.98 14.49,13.91 22.17,2.94 				"/>
				<polygon class="st177" points="22.31,3.08 14.63,14.05 14.56,13.98 22.24,3.01 				"/>
				<polygon class="st178" points="22.38,3.15 14.7,14.12 14.63,14.05 22.31,3.08 				"/>
				<polygon class="st179" points="22.45,3.22 14.77,14.19 14.7,14.12 22.38,3.15 				"/>
				<polygon class="st180" points="22.53,3.29 14.84,14.26 14.77,14.19 22.45,3.22 				"/>
				<polygon class="st181" points="22.6,3.36 14.91,14.33 14.84,14.26 22.53,3.29 				"/>
				<polygon class="st182" points="22.67,3.43 14.98,14.41 14.91,14.33 22.6,3.36 				"/>
				<polygon class="st183" points="22.74,3.5 15.06,14.48 14.98,14.41 22.67,3.43 				"/>
				<polygon class="st184" points="22.81,3.57 15.13,14.55 15.06,14.48 22.74,3.5 				"/>
				<polygon class="st185" points="22.88,3.65 15.2,14.62 15.13,14.55 22.81,3.57 				"/>
				<polygon class="st186" points="22.95,3.72 15.27,14.69 15.2,14.62 22.88,3.65 				"/>
				<polygon class="st187" points="23.02,3.79 15.34,14.76 15.27,14.69 22.95,3.72 				"/>
				<polygon class="st188" points="23.09,3.86 15.41,14.83 15.34,14.76 23.02,3.79 				"/>
				<polygon class="st189" points="23.16,3.93 15.48,14.9 15.41,14.83 23.09,3.86 				"/>
				<polygon class="st190" points="23.24,4 15.55,14.97 15.48,14.9 23.16,3.93 				"/>
				<polygon class="st191" points="23.31,4.07 15.62,15.04 15.55,14.97 23.24,4 				"/>
				<polygon class="st192" points="23.38,4.14 15.69,15.12 15.62,15.04 23.31,4.07 				"/>
				<polygon class="st193" points="23.45,4.21 15.77,15.19 15.69,15.12 23.38,4.14 				"/>
				<polygon class="st194" points="23.52,4.28 15.84,15.26 15.77,15.19 23.45,4.21 				"/>
				<polygon class="st195" points="23.59,4.36 15.91,15.32 15.86,15.28 15.84,15.26 23.52,4.28 				"/>
				<polygon class="st196" points="23.66,4.43 16,15.38 15.91,15.32 23.59,4.36 				"/>
				<polygon class="st197" points="23.73,4.5 16.08,15.43 16,15.38 23.66,4.43 				"/>
				<polygon class="st198" points="23.8,4.57 16.16,15.49 16.08,15.43 23.73,4.5 				"/>
				<polygon class="st199" points="23.87,4.64 16.24,15.55 16.16,15.49 23.8,4.57 				"/>
				<polygon class="st200" points="23.95,4.71 16.32,15.6 16.24,15.55 23.87,4.64 				"/>
				<polygon class="st201" points="24.02,4.78 16.4,15.66 16.32,15.6 23.95,4.71 				"/>
				<polygon class="st202" points="24.09,4.85 16.48,15.72 16.4,15.66 24.02,4.78 				"/>
				<polygon class="st203" points="24.16,4.92 16.56,15.77 16.48,15.72 24.09,4.85 				"/>
				<polygon class="st204" points="24.23,4.99 16.64,15.83 16.56,15.77 24.16,4.92 				"/>
				<polygon class="st205" points="24.3,5.07 16.72,15.89 16.64,15.83 24.23,4.99 				"/>
				<polygon class="st206" points="24.37,5.14 16.81,15.94 16.72,15.89 24.3,5.07 				"/>
				<polygon class="st207" points="24.44,5.21 16.89,16 16.81,15.94 24.37,5.14 				"/>
				<polygon class="st208" points="24.51,5.28 16.97,16.06 16.89,16 24.44,5.21 				"/>
				<polygon class="st209" points="24.58,5.35 17.05,16.11 16.97,16.06 24.51,5.28 				"/>
				<polygon class="st210" points="24.66,5.42 17.13,16.17 17.05,16.11 24.58,5.35 				"/>
				<polygon class="st211" points="24.73,5.49 17.21,16.23 17.13,16.17 24.66,5.42 				"/>
				<polygon class="st212" points="24.8,5.56 17.29,16.28 17.21,16.23 24.73,5.49 				"/>
				<polygon class="st213" points="24.87,5.63 17.37,16.34 17.29,16.28 24.8,5.56 				"/>
				<polygon class="st214" points="24.94,5.7 17.45,16.4 17.37,16.34 24.87,5.63 				"/>
				<polygon class="st215" points="25.01,5.78 17.53,16.45 17.45,16.4 24.94,5.7 				"/>
				<polygon class="st216" points="25.08,5.85 17.62,16.51 17.53,16.45 25.01,5.78 				"/>
				<polygon class="st217" points="25.15,5.92 17.7,16.57 17.62,16.51 25.08,5.85 				"/>
				<polygon class="st218" points="25.22,5.99 17.78,16.62 17.7,16.57 25.15,5.92 				"/>
				<polygon class="st219" points="25.29,6.06 17.86,16.68 17.78,16.62 25.22,5.99 				"/>
				<polygon class="st220" points="25.37,6.13 17.94,16.74 17.86,16.68 25.29,6.06 				"/>
				<polygon class="st221" points="25.44,6.2 18.02,16.79 17.94,16.74 25.37,6.13 				"/>
				<polygon class="st222" points="25.51,6.27 18.1,16.85 18.02,16.79 25.44,6.2 				"/>
				<polygon class="st223" points="25.58,6.34 18.18,16.91 18.1,16.85 25.51,6.27 				"/>
				<polygon class="st224" points="25.65,6.41 18.26,16.96 18.18,16.91 25.58,6.34 				"/>
				<polygon class="st225" points="25.72,6.49 18.34,17.02 18.26,16.96 25.65,6.41 				"/>
				<polygon class="st226" points="25.79,6.56 18.43,17.08 18.34,17.02 25.72,6.49 				"/>
				<polygon class="st227" points="25.86,6.63 18.51,17.13 18.43,17.08 25.79,6.56 				"/>
				<polygon class="st228" points="25.93,6.7 18.59,17.19 18.51,17.13 25.86,6.63 				"/>
				<polygon class="st229" points="26,6.77 18.67,17.25 18.59,17.19 25.93,6.7 				"/>
				<polygon class="st230" points="26.08,6.84 18.75,17.3 18.67,17.25 26,6.77 				"/>
				<polygon class="st231" points="26.15,6.91 18.83,17.36 18.75,17.3 26.08,6.84 				"/>
				<polygon class="st231" points="19.05,17.51 18.83,17.36 26.15,6.91 27.9,8.66 				"/>
			</g>
		</g>
	</g>
</g>
</svg>
