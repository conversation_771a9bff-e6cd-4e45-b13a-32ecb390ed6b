import { useEffect, useState, useRef } from "react";
import axios from "axios";
import QRBox from "../components/QRBox";
import CancelModal from "../components/CancelModal";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { toast } from "react-toastify";
import qs from "qs";

import "../styles/payment.css";

const PaymentPage = () => {
  const [invoice, setInvoice] = useState(null);
  const [user, setUser] = useState(null);
  const [bankInfo, setBankInfo] = useState(null);
  const [error, setError] = useState(null);
  const [remainingTime, setRemainingTime] = useState("");
  const [statusMessage, setStatusMessage] = useState("countdown");
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedBankIndex, setSelectedBankIndex] = useState(0);

  const [bankList, setBankList] = useState([]);
  const createdAt = useRef(Math.floor(Date.now() / 1000));
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const t = params.get("t");
    if (!t) return setError("Thiếu tham số `t`");

    axios
      .get(
        `https://api.pay2s.vn/api/v1/gateway/get_invoice_details?t=${encodeURIComponent(
          t
        )}`
      )
      .then((res) => {
        const data = res.data;
        if (data.status) {
          setInvoice(data.invoice);
          setUser(data.users);
          const defaultBank = {
            description: data.description,
            accountNumber: data.accountNumber,
            bankName: data.bankName,
            shortBankName: data.shortBankName,
            ownerName: data.name,
          };
          setBankList([defaultBank]);
          setBankInfo(defaultBank);
        } else {
          setError(data.message || "Không tìm thấy hóa đơn.");
        }
      })
      .catch(() => setError("Không thể kết nối máy chủ."));
  }, []);
  useEffect(() => {
    if (bankList.length > 0) {
      setBankInfo(bankList[selectedBankIndex]);
    }
  }, [selectedBankIndex, bankList]);
  useEffect(() => {
    const interval = setInterval(() => {
      // const createdAt = useRef(Math.floor(Date.now() / 1000)).current;
      const now = Math.floor(Date.now() / 1000);
      const diff = 540 - (now - createdAt.current);
      if (diff <= 0) {
        setRemainingTime("00:00");
        setStatusMessage("expired");
        clearInterval(interval);

        setTimeout(async () => {
          try {
            const res = await axios.post(
              "https://api.pay2s.vn/v1/gateway/querySession",
              qs.stringify({ invoiceNumber: invoice.invoicenum }),
              {
                headers: {
                  "Content-Type": "application/x-www-form-urlencoded",
                },
              }
            );
            const data = res.data;
            if (data?.redirect_url) {
              window.location.href = data.redirect_url;
            } else {
              alert("Không tìm thấy đường dẫn chuyển hướng.");
            }
          } catch (error) {
            console.error("Lỗi khi chuyển hướng:", error);
          }
        }, 3000);
      } else {
        const m = String(Math.floor(diff / 60)).padStart(2, "0");
        const s = String(diff % 60).padStart(2, "0");
        setRemainingTime(`${m}:${s}`);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [invoice]);

  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const res = await axios.post(
          "https://api.pay2s.vn/v1/gateway/querySession",
          qs.stringify({ invoiceNumber: invoice.invoicenum }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        const data = res.data;

        if (data.code && (data.code === "200" || data.code === 200)) {
          setStatusMessage("success");
          clearInterval(interval);
          setTimeout(() => {
            window.location.href = data.redirect_url;
          }, 1000);
        }
      } catch (err) {
        console.error("Lỗi kiểm tra trạng thái thanh toán:", err);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [invoice]);

  useEffect(() => {
    if (!user) return;

    const logo = "https://docs.pay2s.vn/assets/favicon.png";

    // Chỉ thay favicon nếu khác logo hiện tại
    const favicon = document.querySelector("link[rel~='icon']");
    if (favicon) {
      if (favicon.href !== logo) favicon.href = logo;
    } else {
      const link = document.createElement("link");
      link.rel = "icon";
      link.href = logo;
      document.head.appendChild(link);
    }
  }, [user]);

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("Đã sao chép!");
    });
  };

  if (error) return <div className="container text-danger">{error}</div>;
  if (!invoice || !user || !bankInfo)
    return <div className="container">Đang tải...</div>;

  return (
    <>
      <div className="wrapper">
        <div className="payment-container">
          <div className="payment-header">
            <img
              src={"https://docs.pay2s.vn/assets/logo.png"}
              alt="Logo"
              className="bank-logo"
              width={75}
            />
            <span>{"Giải pháp thanh toán trực tuyến Pay2S"}</span>
          </div>

          <div className="payment-content">
            <div className="payment-info-box">
              <h5>Thông tin thanh toán</h5>

              <div className="bank-info-header">
                <img
                  src={`https://payment.pay2s.vn/public/payment/images/${bankInfo.shortBankName}.svg`}
                  alt={bankInfo.bankName}
                  className="logo-bank"
                />
                <span className="bank-fullname">{bankInfo.bankName}</span>
              </div>
              <div className="info-item">
                <strong>Chủ tài khoản:</strong>
                <div className="value">{bankInfo.ownerName}</div>
              </div>

              <div className="info-item">
                <strong>Số tài khoản:</strong>
                <div
                  className="value-with-button multiline text-ellipsis"
                  title="Nhấn để sao chép"
                  onClick={() => copyToClipboard(bankInfo.accountNumber)}
                  style={{ cursor: "pointer" }}
                >
                  {bankInfo.accountNumber}
                </div>
              </div>

              <div className="info-item">
                <strong>Số tiền:</strong>
                <div
                  className="value-with-button multiline text-ellipsis"
                  title="Nhấn để sao chép"
                  onClick={() => copyToClipboard(invoice.total)}
                  style={{ cursor: "pointer" }}
                >
                  {invoice.total.toLocaleString("vi-VN")}đ
                </div>
              </div>

              <div className="info-item">
                <strong>Nội dung thanh toán:</strong>
                <div
                  className="value-with-button multiline text-ellipsis"
                  title="Nhấn để sao chép"
                  onClick={() => copyToClipboard(bankInfo.description)}
                  style={{ cursor: "pointer" }}
                >
                  {bankInfo.description}
                </div>
              </div>
              <div className="info-item">
                <i>(Vui lòng nhập chính xác nội dung và số tiền)</i>
              </div>
            </div>

            <QRBox
              accountNumber={bankInfo.accountNumber}
              ownerName={bankInfo.ownerName}
              amount={invoice.total}
              description={bankInfo.description}
              bankName={bankInfo.bankName}
              shortBankName={bankInfo.shortBankName}
            />
          </div>
          {bankList.length > 1 && (
            <div className="bank-selection">
              <p>Chọn ngân hàng hoặc ví điện tử để thực hiện thanh toán</p>
              <div className="bank-select-wrapper">
                {bankList.map((bank, index) => (
                  <button
                    key={index}
                    className={`bank-select-box ${
                      index === selectedBankIndex ? "active" : ""
                    }`}
                    onClick={() => setSelectedBankIndex(index)}
                  >
                    <img
                      src={`https://payment.pay2s.vn/public/payment/images/${bank.shortBankName}.svg`}
                      alt={bank.bankName}
                      className="logo-bank"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
          <div className="expire-box">
            {statusMessage === "success" ? (
              <>
                ✅{" "}
                <strong style={{ color: "green" }}>
                  Thanh toán thành công
                </strong>
                , đang chuyển hướng...
              </>
            ) : statusMessage === "expired" ? (
              <>
                ⛔ <strong style={{ color: "red" }}>Đơn hàng đã hết hạn</strong>
                , đang chuyển hướng...
              </>
            ) : (
              <>
                Đơn hàng sẽ hết hạn sau:{" "}
                <strong style={{ color: "red" }}>{remainingTime}</strong>
              </>
            )}
          </div>

          <div className="cancel-link">
            <a
              onClick={() => setShowCancelModal(true)}
              style={{ cursor: "pointer" }}
            >
              Hủy giao dịch
            </a>
          </div>
        </div>
      </div>
      <CancelModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onConfirm={async () => {
          try {
            const res = await axios.post(
              "https://api.pay2s.vn/v1/gateway/querySession",
              qs.stringify({ invoiceNumber: invoice.invoicenum }),
              {
                headers: {
                  "Content-Type": "application/x-www-form-urlencoded",
                },
              }
            );
            const data = res.data;
            if (data?.redirect_url) {
              window.location.href = data.redirect_url;
            } else {
              alert("Không lấy được link chuyển hướng!");
            }
          } catch (err) {
            console.error(err);
            alert("Có lỗi xảy ra khi xử lý huỷ giao dịch.");
          }
        }}
        companyName={"Cổng thanh toán Pay2S"}
      />
      <footer className="payment-footer">
        Pay2S v2.0.1 © 2025 - Cổng thanh toán online thuộc{" "}
        {"Pay2S"}
      </footer>
    </>
  );
};

export default PaymentPage;
