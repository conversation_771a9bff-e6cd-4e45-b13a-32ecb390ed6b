import { useEffect, useState } from "react";
import axios from "axios";
import QRBox from "../components/QRBox";
import CancelModal from "../components/CancelModal";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCopy } from "@fortawesome/free-solid-svg-icons";

import "../styles/payment.css";

const PaymentPage = () => {
  const [invoice, setInvoice] = useState(null);
  const [user, setUser] = useState(null);
  const [bankInfo, setBankInfo] = useState(null);
  const [error, setError] = useState(null);
  const [remainingTime, setRemainingTime] = useState("");
  const [statusMessage, setStatusMessage] = useState("countdown");
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedBankIndex, setSelectedBankIndex] = useState(0);
  const [bankList, setBankList] = useState([]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const t = params.get("t");
    if (!t) return setError("Thiếu tham số `t`");

    axios
      .get(
        `https://payment.pay2s.vn/v1/gateway/get_invoice_details?t=${encodeURIComponent(
          t
        )}`
      )
      .then((res) => {
        const data = res.data;
        if (data.status) {
          const now = Math.floor(Date.now() / 1000);
          data.invoice.expire_time = now + 600;
          setInvoice(data.invoice);
          setUser(data.users);
          if (data.banks?.length) {
            const banksWithOwner = data.banks.map((bank) => ({
    ...bank,
    ownerName: bank.name, // gán lại cho đúng key được dùng
  }));
  setBankList(banksWithOwner);
  setBankInfo(banksWithOwner[0]);
          } else {
            const defaultBank = {
              accountNumber: data.accountNumber,
              bankName: data.bankName,
              shortBankName: data.shortBankName,
              ownerName: data.name,
            };
            setBankList([defaultBank]);
            setBankInfo(defaultBank);
          }
        } else {
          setError(data.message || "Không tìm thấy hóa đơn.");
        }
      })
      .catch(() => setError("Không thể kết nối máy chủ."));
  }, []);
  useEffect(() => {
    if (bankList.length > 0) {
      setBankInfo(bankList[selectedBankIndex]);
    }
  }, [selectedBankIndex, bankList]);
  useEffect(() => {
    const interval = setInterval(() => {
      if (invoice?.expire_time) {
        const now = Math.floor(Date.now() / 1000);
        const diff = invoice.expire_time - now;
        if (diff <= 0) {
          setRemainingTime("00:00");
          setStatusMessage("expired");
          clearInterval(interval);

          setTimeout(async () => {
            try {
              const params = new URLSearchParams(window.location.search);
              const t = params.get("t");
              const res = await axios.post(
                "https://payment.pay2s.vn/api/generate-redirect",
                { t }
              );
              const data = res.data;
              if (data?.redirectUrl) {
                window.location.href = data.redirectUrl;
              } else {
                alert("Không tìm thấy đường dẫn chuyển hướng.");
              }
            } catch (error) {
              console.error("Lỗi khi chuyển hướng:", error);
            }
          }, 3000);
        } else {
          const m = String(Math.floor(diff / 60)).padStart(2, "0");
          const s = String(diff % 60).padStart(2, "0");
          setRemainingTime(`${m}:${s}`);
        }
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [invoice]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const t = params.get("t");
    if (!t || !invoice) return;

    const interval = setInterval(async () => {
      try {
        const res = await axios.post(
          "https://payment.pay2s.vn/api/generate-redirect",
          {
            t: t,
          }
        );
        const data = res.data;

        if (data.success && data.paid) {
          setStatusMessage("success");
          clearInterval(interval);
          setTimeout(() => {
            window.location.href = data.redirectUrl;
          }, 1000);
        }
      } catch (err) {
        console.error("Lỗi kiểm tra trạng thái thanh toán:", err);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [invoice]);

useEffect(() => {
  if (!user) return;

  const logo = user?.company_logo?.trim() ? user.company_logo : "https://docs.pay2s.vn/assets/favicon.png";

  // Chỉ thay favicon nếu khác logo hiện tại
  const favicon = document.querySelector("link[rel~='icon']");
  if (favicon) {
    if (favicon.href !== logo) favicon.href = logo;
  } else {
    const link = document.createElement("link");
    link.rel = "icon";
    link.href = logo;
    document.head.appendChild(link);
  }
}, [user]);

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  if (error) return <div className="container text-danger">{error}</div>;
  if (!invoice || !user || !bankInfo)
    return <div className="container">Đang tải...</div>;

  return (
    <>
      <div className="wrapper">
        <div className="payment-container">
          <div className="payment-header">
            <img
              src={
                user.company_logo?.trim()
                  ? user.company_logo
                  : "https://docs.pay2s.vn/assets/logo.png"
              }
              alt="Logo"
              className="bank-logo"
              width={75}
            />
            <span>
              {invoice?.memchart_name?.trim()
                ? invoice.memchart_name
                : user.company_name || "Cổng thanh toán trực tuyến - Pay2S"}
            </span>
          </div>

          <div className="payment-content">
            <div className="payment-info-box">
              <h5>Thông tin thanh toán</h5>

              <div className="bank-info-header">
                <img
                  src={`https://payment.pay2s.vn/public/payment/images/${bankInfo.shortBankName}.svg`}
                  alt={bankInfo.bankName}
                  className="logo-bank"
                />
                <span className="bank-fullname">{bankInfo.bankName}</span>
              </div>
              <div className="info-item">
                <strong>Chủ tài khoản:</strong>
                <div className="value">{bankInfo.ownerName}</div>
              </div>

              <div className="info-item">
                <strong>Số tài khoản:</strong>
                <div className="value-with-button">
                  {bankInfo.accountNumber}
                  <button
                    onClick={() => copyToClipboard(bankInfo.accountNumber)}
                    className="copy-icon-button"
                    title="Sao chép"
                  >
                    <FontAwesomeIcon icon={faCopy} />
                  </button>
                </div>
              </div>

              <div className="info-item">
                <strong>Số tiền:</strong>
                <div className="value-with-button">
                  {invoice.amount.toLocaleString("vi-VN")}đ
                  <button
                    onClick={() => copyToClipboard(invoice.amount)}
                    className="copy-icon-button"
                    title="Sao chép"
                  >
                    <FontAwesomeIcon icon={faCopy} />
                  </button>
                </div>
              </div>

              <div className="info-item">
                <strong>Nội dung chuyển khoản:</strong>
                <div className="value-with-button multiline">
                   <span className="text-ellipsis">{invoice.description}</span>
                  <button
                    onClick={() => copyToClipboard(invoice.description)}
                    className="copy-icon-button"
                    title="Sao chép"
                  >
                    <FontAwesomeIcon icon={faCopy} />
                  </button>
                </div>
              </div>
              <div className="info-item">
                <i>(Vui lòng nhập chính xác nội dung và số tiền)</i>
              </div>
            </div>

            <QRBox
              accountNumber={bankInfo.accountNumber}
              ownerName={bankInfo.ownerName}
              amount={invoice.amount}
              description={invoice.description}
              bankName={bankInfo.bankName}
              shortBankName={bankInfo.shortBankName}
            />
          </div>
          {bankList.length > 1 && (
            <div className="bank-selection">
              <p>Chọn ngân hàng hoặc ví điện tử để thực hiện thanh toán</p>
              <div className="bank-select-wrapper">
                {bankList.map((bank, index) => (
                  <button
                    key={index}
                    className={`bank-select-box ${
                      index === selectedBankIndex ? "active" : ""
                    }`}
                    onClick={() => setSelectedBankIndex(index)}
                  >
                    <img
                      src={`https://payment.pay2s.vn/public/payment/images/${bank.shortBankName}.svg`}
                      alt={bank.bankName}
                      className="logo-bank"
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
          <div className="expire-box">
            {statusMessage === "success" ? (
              <>
                ✅{" "}
                <strong style={{ color: "green" }}>
                  Thanh toán thành công
                </strong>
                , đang chuyển hướng...
              </>
            ) : statusMessage === "expired" ? (
              <>
                ⛔ <strong style={{ color: "red" }}>Đơn hàng đã hết hạn</strong>
                , đang chuyển hướng...
              </>
            ) : (
              <>
                Đơn hàng sẽ hết hạn sau:{" "}
                <strong style={{ color: "red" }}>{remainingTime}</strong>
              </>
            )}
          </div>

          <div className="cancel-link">
            <a
              onClick={() => setShowCancelModal(true)}
              style={{ cursor: "pointer" }}
            >
              Hủy giao dịch
            </a>
          </div>
        </div>
      </div>
      <CancelModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onConfirm={async () => {
          try {
            const params = new URLSearchParams(window.location.search);
            const t = params.get("t");
            if (!t || !invoice) return;
            const res = await axios.post(
              "https://payment.pay2s.vn/api/generate-redirect",
              { t: t }
            );
            const data = res.data;
            if (data?.redirectUrl) {
              window.location.href = data.redirectUrl;
            } else {
              alert("Không lấy được link chuyển hướng!");
            }
          } catch (err) {
            alert("Có lỗi xảy ra khi xử lý huỷ giao dịch.");
          }
        }}
        companyName={
          invoice?.memchart_name?.trim()
            ? invoice.memchart_name
            : user.company_name || "Cổng thanh toán Pay2S"
        }
      />
      <footer className="payment-footer">
  Pay2S v2.0.1 © 2025 - Cổng thanh toán online thuộc{" "}
  {invoice?.memchart_name?.trim()
    ? invoice.memchart_name
    : user.company_name || "Cổng thanh toán trực tuyến - Pay2S"}
</footer>
    </>
    
  );
};

export default PaymentPage;
