body {
  background-image: url(../images/bg-acb.svg), linear-gradient(to top, #fff, #e2edff);
  background-repeat: no-repeat;
  background-position: top right;
  background-size: contain;
  margin: 0;
  font-family: "Segoe UI", <PERSON><PERSON>a, sans-serif;
  background-color: #f8f8f8;
  display: flex;
  justify-content: center;
  align-items: start;
  min-height: 100vh;
}
svg:not(:root).svg-inline--fa, svg:not(:host).svg-inline--fa
{
  color: #2b7c51 !important;
}
.wrapper {
  display: flex;
  justify-content: center;
  padding: 40px 0;
  padding-bottom: 0;
}

.payment-container {
  max-width: 1280px;
  width: 100%;
  margin: 40px auto;
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.07);
}

.payment-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.bank-logo {
  height: 40px;
  object-fit: contain;
}

.payment-header img {
  height: 48px;
}

.payment-header span {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.payment-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
  padding: 30px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.payment-info-box {
  flex: 1;
  background: #fff;
  min-height: 340px;
  padding: 0px 25px;
}

.payment-info-box h5,
.qr-box h5 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  margin-top: 0;
  color: #333;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;
}

.info-item strong {
  min-width: 180px;
  font-weight: 600;
  color: #555;
}

.info-item .value {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #111;
}

.copy-btn {
  background-color: #e6e6e6;
  border: none;
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: 0.2s;
}

.copy-btn:hover {
  background-color: #d4d4d4;
}

.expire-box {
  background-color: #fff3cd;
  color: #856404;
  padding: 14px;
  border-radius: 8px;
  margin-top: 35px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  border: 1px solid #ffeeba;
}

.expire-box span,
.expire-box strong {
  color: #ff0000;
}

.cancel-link {
  text-align: center;
  margin-top: 20px;
}

.cancel-link a {
  color: #e40000;
  font-size: 15px;
  text-decoration: none;
  font-weight: 500;
}

.cancel-link a:hover {
  text-decoration: underline;
}

.container.text-danger {
  color: red;
  text-align: center;
  margin-top: 60px;
  font-size: 18px;
  font-weight: bold;
}

.qr-modern-wrapper {
  background: #f5f8fe;
  padding: 30px 20px;
  border-radius: 10px;
  text-align: center;
}

.qr-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff !important;
  margin-bottom: 15px;
}

.qr-img {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  background: white;
  padding: 8px;
}

.qr-note {
  font-size: 13px;
  color: #ffffff;
  margin-top: 12px;
}

.qr-loading {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-style: italic;
  font-size: 14px;
}

.qr-frame {
  background: #ffffff;
  padding: 15px;
  border-radius: 10px;
  display: inline-block;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.qr-frame img {
  width: 200px;
  height: 200px;
  object-fit: contain;
}

.bank-info-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.bank-info-header img {
  height: 20px;
  object-fit: contain;
  overflow: hidden;
}

.bank-fullname {
  font-size: 16px;
  font-weight: 500;
  color: #222;
}

.apply-bg-vcb {
  background-image: url(./images/bg-vcb.svg),
    linear-gradient(to top, #fff, #e7fbef);
  background-repeat: no-repeat;
  background-position: right;
  background-size: contain;
}

.apply-bg-acb {
  background: url(https://static.mservice.io/jk/momo2020/img/intro/qrcode-pattern.png)
      no-repeat,
    linear-gradient(to bottom, rgba(85, 162, 245, 1), rgb(0 56 168));
  background-repeat: no-repeat;
  background-size: cover;
}

.apply-bg-vtb {
   background: url(https://static.mservice.io/jk/momo2020/img/intro/qrcode-pattern.png)
      no-repeat,
    linear-gradient(to bottom, #005993 36.46%, #EA114E 96.25%),#C4C4C4;
  background-repeat: no-repeat;
  background-size: cover;
}

.apply-bg-mbb {
  background: url(https://static.mservice.io/jk/momo2020/img/intro/qrcode-pattern.png)
      no-repeat,
    linear-gradient(to bottom, rgba(48, 142, 235, 1), rgba(0, 84, 166, 1));
  background-repeat: no-repeat;
  background-position: 78% top;
  background-size: cover;
}

.apply-bg-bidv {
  background: url(https://static.mservice.io/jk/momo2020/img/intro/qrcode-pattern.png)      no-repeat,    linear-gradient(to bottom, #00bfae, #0066ad);
  background-repeat: no-repeat;
  background-position: 78% top;
  background-size: cover;
}

.apply-bg-seab {
  background: url(https://static.mservice.io/jk/momo2020/img/intro/qrcode-pattern.png) no-repeat, linear-gradient(to bottom, rgb(217 32 39), rgb(233 99 104));
  background-repeat: no-repeat;
  background-position: 78% top;
  background-size: cover;
}

.qr-section {
  padding: 24px;
  border-radius: 10px;
  text-align: center;
  position: relative;
}

.payment-wrapper {
  display: flex;
  gap: 30px;
  justify-content: center;
  align-items: flex-start;
  padding: 24px;
  background: #fff;
  border-radius: 10px;
  flex-wrap: wrap; /* Cho phép xuống dòng */
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  width: 160px;
  font-weight: 500;
}

.value-with-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 180px; /* hoặc auto + padding-right */
  gap: 8px;
  color: #111;
  font-weight: 600;
}
.value-with-button.multiline {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  word-break: break-all;
  flex-wrap: nowrap;
}

.value-with-button.multiline span {
  flex: 1;
  min-width: 0;
  word-break: break-word;
  white-space: normal;
}

.copy-icon-button {
  flex-shrink: 0;
}
.value-with-button span {
  margin-right: 12px;
  white-space: nowrap;
}

.copy-button {
  background-color: #e0e0e0;
  border: none;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.payment-content {
  display: flex;
  flex-direction: row;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.download-qr-btn {
  display: none;
  width: fit-content;
  margin: 5px auto 0;
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-box {
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2);
  animation: popIn 0.3s ease-out;
}

.modal-box h3 {
  margin: 0;
  color: #b00020;
  font-size: 20px;
}

.modal-box p {
  color: #444;
  margin-top: 12px;
  font-size: 15px;
}

@keyframes popIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
@media (max-width: 992px) and (min-width: 769px) {
  .payment-container {
    padding: 20px;
  }

  .payment-info-box {
    padding: 15px;
    font-size: 15px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
  }


  .info-item .value-with-button {
    margin-top: 6px;
  }

  .logo-bank {
    width: 28px;
    height: 28px;
  }
  .bank-select-wrapper {
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .bank-select-box {
    width: 70px;
    height: 70px;
  }
  .bank-selection p {
    color: black;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-top: 0;
  }
  .payment-info-box {
    margin-top: -20px !important;
  }
  .payment-header {
    padding-left: 20px;
  }
  .bank-info-header {
    margin-bottom: 0;
  }
  .info-item {
    margin-bottom: 0;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-bottom: 0;
  }
  .bank-selection p {
    color: black;
  }
  .payment-info-box {
    margin-top: -20px;
  }
  .expire-box {
    margin-top: 0;
  }
  .cancel-link {
    margin-top: 10px;
  }

  .bank-select-box img.logo-bank {
    width: 45px !important;
    height: 45px !important;
    margin-bottom: 0;
  }
}

@media (max-width: 768px) and (min-width: 391px) {

  
  .payment-info-box h5,
  .qr-box h5 {
    margin-top: 15px;
  }
  .qr-title {
    color: #333 !important;
  }
  .bank-select-box img.logo-bank {
    width: 40px !important;
    height: 40px !important;
    margin-bottom: 0 !important;
  }
  .qr-note {
    color: #333;
  }
  button.copy-icon-button {
    background-color: #f9f9f900;
  }
  .bank-select-box {
    width: 60px !important;
    height: 60px !important;
  }
  .payment-header {
    padding-left: 0px;
  }
  .bank-info-header {
    margin-bottom: 0;
  }
  .info-item {
    margin-bottom: 0;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-bottom: 5px;
    font-size: 15px;
  }
  .bank-selection p {
    color: black;
    font-size: 14px;
  }
  .payment-info-box {
    margin-top: -20px;
  }
  .expire-box {
    margin-top: 0;
  }
  .cancel-link {
    margin-top: 10px;
  }
  .qr-box img {
    max-width: 150px;
    height: auto;
    margin: 0 auto;
  }
  .qr-note {
    display: none;
  }
  .qr-box {
    padding-bottom: 10px !important;
  }
  .download-qr-btn {
    display: block;
  }
  .payment-wrapper {
    flex-direction: column;
    gap: 24px;
  }
  .payment-header img {
    max-width: 20%;
  }
  .payment-info-box {
    padding: 0px;
    min-height: 0px;
  }

  .info-item .value {
    display: flex;
    align-items: center;
    gap: 30px;
    font-weight: 600;
    color: #111;
  }
  .payment-content {
    padding: 0 16px;
    gap: 0;
  }
  .bank-info-header img {
    height: 20px;
    object-fit: contain;
    overflow: hidden;
  }
  .apply-bg-vcb,
  .apply-bg-seab,
  .apply-bg-bidv,
  .apply-bg-acb,
  .apply-bg-vtb,
  .apply-bg-mbb {
    background: none !important;
  }

  .qr-container {
    background: none !important;
    box-shadow: none !important;
  }
  .qr-box {
    background: #fff !important;
    border-radius: 10px;
    padding: 0px;
    box-shadow: unset;
    text-align: center;
  }

  .qr-box p {
    margin-top: 12px;
    font-size: 14px;
    color: #555;
  }
  .payment-container {
    margin: 0px;
    padding-top: 20px;
  }
  .wrapper {
    padding: 0;
    padding-bottom: 10px;
  }
  .qr-section {
    padding: 0px;
  }
      .info-item-note {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
    text-align: center;
  }
}
@media (max-width: 430px) and (min-width: 381px) {
      .info-item-note {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
    text-align: center;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-top: 15px;
  }
  .qr-title {
    color: #333 !important;
  }
  .bank-select-box img.logo-bank {
    width: 40px !important;
    height: 40px !important;
    margin-bottom: 0 !important;
  }
  .qr-note {
    color: #333;
  }
  button.copy-icon-button {
    background-color: #f9f9f900;
  }
  .bank-select-box {
    width: 60px !important;
    height: 60px !important;
  }
  .payment-header {
    padding-left: 15px;
  }
  .payment-footer {
    padding-right: 40px !important;
        padding-left: 40px !important;
}
  .bank-info-header {
    margin-bottom: 0;
  }
  .info-item {
    margin-bottom: 0;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-bottom: 5px;
    font-size: 15px;
  }
  .bank-selection p {
    color: black;
    font-size: 14px;
  }
  .payment-info-box {
    margin-top: -20px;
  }
  .expire-box {
    margin-top: 0;
  }
  .cancel-link {
    margin-top: 10px;
  }
  .qr-box img {
    max-width: 150px;
    height: auto;
    margin: 0 auto;
  }
  .qr-note {
    display: none;
  }
  .qr-box {
    padding-bottom: 10px !important;
  }
  .download-qr-btn {
    display: block;
  }
  .payment-wrapper {
    flex-direction: column;
    gap: 24px;
  }
  .payment-header img {
    max-width: 20%;
  }
  .payment-info-box {
    padding: 0px;
    min-height: 0px;
  }

  .info-item .value {
    display: flex;
    align-items: center;
    gap: 30px;
    font-weight: 600;
    color: #111;
  }
  .payment-content {
    padding: 0 16px;
    gap: 0;
  }
  .bank-info-header img {
    height: 20px;
    object-fit: contain;
    overflow: hidden;
  }
  .apply-bg-vcb,
  .apply-bg-seab,
  .apply-bg-bidv,
  .apply-bg-acb,
  .apply-bg-vtb,
  .apply-bg-mbb {
    background: none !important;
  }

  .qr-container {
    background: none !important;
    box-shadow: none !important;
  }
  .qr-box {
    background: #fff !important;
    border-radius: 10px;
    padding: 0px;
    box-shadow: unset;
    text-align: center;
  }

  .qr-box p {
    margin-top: 12px;
    font-size: 14px;
    color: #555;
  }
  .payment-container {
    margin: 0px;
    padding-top: 20px;
  }
  .wrapper {
    padding: 0;
    padding-bottom: 10px;
  }
  .qr-section {
    padding: 0px;
  }
}



html,
body {
  overflow-x: hidden !important;
  width: 100%;
}

.bank-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.bank-option {
  flex: 1 1 150px;
  border: 1px solid #ccc;
  background: #fff;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-radius: 8px;
  transition: 0.2s;
}

.bank-option.active {
  border-color: #007bff;
  background-color: #f0f8ff;
}

.bank-option img {
  width: 40px;
  height: 40px;
  margin-bottom: 8px;
}
.bank-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
img.logo-bank {
  width: 60px;
  height: 60px;
}
.bank-selection {
  text-align: center;
  margin-bottom: 20px;
}

.bank-select-wrapper {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 10px;
}

.bank-select-box {
  width: 90px;
  height: 90px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: 0.2s ease;
}

.bank-select-box:hover {
  border-color: #00b894;
  background-color: #f2fffc;
  box-shadow: 0 0 10px rgba(255, 165, 0, 0.4); /* Đổ bóng */
  transform: translateY(-2px); /* Nổi lên nhẹ */
}

.bank-select-box.active {
  background: #f0f8ff;
  border: 2px solid #ffa500;
}

.bank-select-box img.logo-bank {
  width: 50px;
  height: 50px;
}
.copy-icon-btn {
  margin-left: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #555;
  font-size: 16px;
}

.payment-footer {
  text-align: center;
  padding: 0px 0;
  font-size: 14px;
  color: #666;
  background-color: transparent;
  margin-top: 0px;
}


@media (max-width: 380px)  {
      .info-item-note {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
    text-align: center;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-top: 15px;
  }
  .qr-title {
    color: #333 !important;
  }
  .bank-select-box img.logo-bank {
    width: 40px !important;
    height: 40px !important;
    margin-bottom: 0 !important;
  }
  .qr-note {
    color: #333;
  }
  button.copy-icon-button {
    background-color: #f9f9f900;
  }
  .bank-select-box {
    width: 60px !important;
    height: 60px !important;
  }
  .payment-header {
    padding-left: 25px;
  }
  .payment-footer {
    padding-right: 40px !important;
        padding-left: 40px !important;
}
  .bank-info-header {
    margin-bottom: 0;
  }
  .info-item {
    margin-bottom: 0;
  }
  .payment-info-box h5,
  .qr-box h5 {
    margin-bottom: 5px;
    font-size: 15px;
  }
  .bank-selection p {
    color: black;
    font-size: 14px;
  }
  .payment-info-box {
    margin-top: -20px;
  }
  .expire-box {
    margin-top: 0;
  }
  .cancel-link {
    margin-top: 10px;
  }
  .qr-box img {
    max-width: 150px;
    height: auto;
    margin: 0 auto;
  }
  .qr-note {
    display: none;
  }
  .qr-box {
    padding-bottom: 10px !important;
  }
  .download-qr-btn {
    display: block;
  }
  .payment-wrapper {
    flex-direction: column;
    gap: 24px;
  }
  .payment-header img {
    max-width: 20%;
  }
  .payment-info-box {
    padding: 0px;
    min-height: 0px;
  }

  .info-item .value {
    display: flex;
    align-items: center;
    gap: 30px;
    font-weight: 600;
    color: #111;
  }
  .payment-content {
    padding: 0 16px;
    gap: 0;
    padding-left: 25px;
  }
  .bank-info-header img {
    height: 20px;
    object-fit: contain;
    overflow: hidden;
  }
  .apply-bg-vcb,
  .apply-bg-seab,
  .apply-bg-bidv,
  .apply-bg-acb,
  .apply-bg-vtb,
  .apply-bg-mbb {
    background: none !important;
  }

  .qr-container {
    background: none !important;
    box-shadow: none !important;
  }
  .qr-box {
    background: #fff !important;
    border-radius: 10px;
    padding: 0px;
    box-shadow: unset;
    text-align: center;
  }

  .qr-box p {
    margin-top: 12px;
    font-size: 14px;
    color: #555;
  }
  .payment-container {
    margin: 0px;
    padding-top: 20px;
  }
  .wrapper {
    padding: 0;
    padding-bottom: 10px;
  }
  .qr-section {
    padding: 0px;
  }
}